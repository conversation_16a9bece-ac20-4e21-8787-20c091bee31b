"""
Distributed Bus Framework for Multi-Personal-Node Applications

This framework provides service discovery, RPC mechanisms, and event delivery
for building applications that work seamlessly across multiple personal nodes.
"""

from .distributed_bus import DistributedBus
from .decorators import service, event_subscriber

__version__ = "1.0.0"
__all__ = ["DistributedBus", "service", "event_subscriber"]
