"""
Smart Service Proxy that automatically handles service discovery and failover.
"""

import time
from typing import Any, Optional


class SmartServiceProxy:
    """
    A smart proxy that automatically handles service discovery, validation, and failover.
    
    This proxy:
    1. Always tries to find the best available remote service
    2. Automatically validates services before use
    3. Falls back to local service if no remote services are available
    4. Retries and reconnects on failures
    """
    
    def __init__(self, service_name: str, bus):
        self.service_name = service_name
        self.bus = bus
        self._current_proxy = None
        self._last_lookup_time = 0
        self._lookup_interval = 5.0  # Re-lookup every 5 seconds
        self._local_service = None
    
    def _get_current_service(self):
        """Get the current best available service."""
        current_time = time.time()
        
        # Check if we need to refresh our service lookup
        if (current_time - self._last_lookup_time > self._lookup_interval or 
            self._current_proxy is None):
            
            print(f"Looking up fresh service for {self.service_name}")
            
            # Try to find remote services (skip validation for now due to SSL issues)
            remote_proxy = self.bus.rpc_client.lookup_service(
                self.service_name, timeout=2.0
            )
            
            if remote_proxy:
                print(f"Using remote service for {self.service_name}")
                self._current_proxy = remote_proxy
                self._last_lookup_time = current_time
                return self._current_proxy
            
            # No valid remote service, try to get/create local service
            if self._local_service is None:
                print(f"Creating local service for {self.service_name}")
                self._local_service = self._create_local_service()
            
            if self._local_service:
                print(f"Using local service for {self.service_name}")
                self._current_proxy = self._local_service
                self._last_lookup_time = current_time
                return self._current_proxy
        
        return self._current_proxy
    
    def _create_local_service(self):
        """Create a local service instance."""
        try:
            from .rpc_framework import get_service_registry
            registry = get_service_registry()
            
            if self.service_name in registry:
                service_registration = registry[self.service_name]
                service_instance = service_registration.create_instance()
                
                # Set the bus reference for event publishing
                if hasattr(service_instance, '_set_bus'):
                    service_instance._set_bus(self.bus)
                
                # Register it as a local service in the bus
                self.bus.rpc_server.register_service(self.service_name, service_instance)
                
                return service_instance
            
        except Exception as e:
            print(f"Failed to create local service {self.service_name}: {e}")
        
        return None
    
    def _call_with_retry(self, method_name: str, *args, **kwargs):
        """Call a method with automatic retry and failover."""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                service = self._get_current_service()
                if service is None:
                    raise Exception(f"No service available for {self.service_name}")
                
                # Get the method and call it
                method = getattr(service, method_name)
                result = method(*args, **kwargs)
                
                return result
                
            except Exception as e:
                print(f"Service call failed (attempt {attempt + 1}/{max_retries}): {e}")
                
                # Clear current proxy to force re-lookup
                self._current_proxy = None
                self._last_lookup_time = 0
                
                if attempt == max_retries - 1:
                    # Last attempt failed, raise the exception
                    raise e
                
                # Wait a bit before retrying
                time.sleep(0.5)
        
        raise Exception(f"All retry attempts failed for {self.service_name}.{method_name}")
    
    def __getattr__(self, name: str) -> Any:
        """Dynamically handle method calls."""
        if name.startswith('_'):
            # Don't proxy private methods
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")
        
        def method_wrapper(*args, **kwargs):
            return self._call_with_retry(name, *args, **kwargs)
        
        return method_wrapper
    
    def __repr__(self):
        return f"SmartServiceProxy({self.service_name})"
