"""
Personal Certificate Authority for secure communication between personal nodes.
"""

import os
import socket
import ipaddress
import hashlib
import getpass
from datetime import datetime, timedelta
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.serialization import Encoding, PrivateFormat, NoEncryption
import ssl


class PersonalCA:
    """Personal Certificate Authority for authenticating personal nodes."""
    
    def __init__(self, ca_dir="~/.distributed_bus_ca"):
        self.ca_dir = os.path.expanduser(ca_dir)
        os.makedirs(self.ca_dir, exist_ok=True)

        self.ca_cert_path = os.path.join(self.ca_dir, "ca_cert.pem")
        self.ca_key_path = os.path.join(self.ca_dir, "ca_key.pem")
        self.node_cert_path = os.path.join(self.ca_dir, "node_cert.pem")
        self.node_key_path = os.path.join(self.ca_dir, "node_key.pem")
        self.user_id_path = os.path.join(self.ca_dir, "user_id.txt")

        self._ensure_ca_exists()
        self._ensure_node_cert_exists()
        self._ensure_user_id_exists()
    
    def _ensure_ca_exists(self):
        """Create CA certificate and key if they don't exist."""
        if os.path.exists(self.ca_cert_path) and os.path.exists(self.ca_key_path):
            return
        
        # Generate CA private key
        ca_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Create CA certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Personal CA"),
            x509.NameAttribute(NameOID.COMMON_NAME, "Personal CA Root"),
        ])
        
        ca_cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            ca_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=3650)  # 10 years
        ).add_extension(
            x509.BasicConstraints(ca=True, path_length=None),
            critical=True,
        ).add_extension(
            x509.KeyUsage(
                key_cert_sign=True,
                crl_sign=True,
                digital_signature=False,
                content_commitment=False,
                key_encipherment=False,
                data_encipherment=False,
                key_agreement=False,
                encipher_only=False,
                decipher_only=False,
            ),
            critical=True,
        ).add_extension(
            x509.SubjectKeyIdentifier.from_public_key(ca_key.public_key()),
            critical=False,
        ).add_extension(
            x509.AuthorityKeyIdentifier.from_issuer_public_key(ca_key.public_key()),
            critical=False,
        ).sign(ca_key, hashes.SHA256())
        
        # Save CA certificate and key
        with open(self.ca_cert_path, "wb") as f:
            f.write(ca_cert.public_bytes(Encoding.PEM))
        
        with open(self.ca_key_path, "wb") as f:
            f.write(ca_key.private_bytes(
                Encoding.PEM,
                PrivateFormat.PKCS8,
                NoEncryption()
            ))
    
    def _ensure_node_cert_exists(self):
        """Create node certificate if it doesn't exist."""
        if os.path.exists(self.node_cert_path) and os.path.exists(self.node_key_path):
            return

        # Load CA certificate and key
        with open(self.ca_cert_path, "rb") as f:
            ca_cert = x509.load_pem_x509_certificate(f.read())

        with open(self.ca_key_path, "rb") as f:
            ca_key = serialization.load_pem_private_key(f.read(), password=None)

        # Generate node private key
        node_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        # Get hostname for certificate
        hostname = socket.gethostname()

        # Create node certificate
        subject = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Personal Node"),
            x509.NameAttribute(NameOID.COMMON_NAME, hostname),
        ])

        # Get all network IP addresses for this machine
        san_list = [
            x509.DNSName(hostname),
            x509.DNSName("localhost"),
            x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
        ]

        # Add all network interface IP addresses
        try:
            import netifaces
            for interface in netifaces.interfaces():
                try:
                    addrs = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addrs:
                        for addr_info in addrs[netifaces.AF_INET]:
                            ip_str = addr_info['addr']
                            if ip_str != '127.0.0.1':  # Skip loopback
                                try:
                                    ip_addr = ipaddress.IPv4Address(ip_str)
                                    san_list.append(x509.IPAddress(ip_addr))
                                except ipaddress.AddressValueError:
                                    pass  # Skip invalid IPs
                except (KeyError, ValueError):
                    pass  # Skip interfaces without IPv4
        except ImportError:
            # Fallback: try to get the primary network IP
            try:
                # Connect to a remote address to determine our local IP
                with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                    s.connect(("*******", 80))
                    local_ip = s.getsockname()[0]
                    if local_ip != '127.0.0.1':
                        san_list.append(x509.IPAddress(ipaddress.IPv4Address(local_ip)))
            except Exception:
                pass  # If we can't determine IP, just use what we have

        node_cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            ca_cert.subject
        ).public_key(
            node_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.utcnow()
        ).not_valid_after(
            datetime.utcnow() + timedelta(days=365)  # 1 year
        ).add_extension(
            x509.SubjectAlternativeName(san_list),
            critical=False,
        ).add_extension(
            x509.SubjectKeyIdentifier.from_public_key(node_key.public_key()),
            critical=False,
        ).add_extension(
            x509.AuthorityKeyIdentifier.from_issuer_public_key(ca_key.public_key()),
            critical=False,
        ).sign(ca_key, hashes.SHA256())
        
        # Save node certificate and key
        with open(self.node_cert_path, "wb") as f:
            f.write(node_cert.public_bytes(Encoding.PEM))
        
        with open(self.node_key_path, "wb") as f:
            f.write(node_key.private_bytes(
                Encoding.PEM,
                PrivateFormat.PKCS8,
                NoEncryption()
            ))
    
    def get_ssl_context(self, server=False):
        """Get SSL context for secure communication."""
        context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH if not server else ssl.Purpose.CLIENT_AUTH)

        # Both server and client need to load their certificate for mutual TLS
        context.load_cert_chain(self.node_cert_path, self.node_key_path)

        context.load_verify_locations(self.ca_cert_path)
        context.verify_mode = ssl.CERT_REQUIRED
        context.check_hostname = False  # We're using personal CA

        return context
    
    def verify_peer_certificate(self, peer_cert_der):
        """Verify if a peer certificate is signed by our CA."""
        try:
            peer_cert = x509.load_der_x509_certificate(peer_cert_der)
            
            # Load CA certificate
            with open(self.ca_cert_path, "rb") as f:
                ca_cert = x509.load_pem_x509_certificate(f.read())
            
            # Verify signature
            ca_public_key = ca_cert.public_key()
            ca_public_key.verify(
                peer_cert.signature,
                peer_cert.tbs_certificate_bytes,
                peer_cert.signature_algorithm_oid._name
            )
            
            return True
        except Exception:
            return False
    
    def get_ca_fingerprint(self):
        """Get CA certificate fingerprint for identification."""
        with open(self.ca_cert_path, "rb") as f:
            ca_cert = x509.load_pem_x509_certificate(f.read())

        fingerprint = hashes.Hash(hashes.SHA256())
        fingerprint.update(ca_cert.public_bytes(Encoding.DER))
        return fingerprint.finalize().hex()

    def _ensure_user_id_exists(self):
        """Create user ID if it doesn't exist."""
        if os.path.exists(self.user_id_path):
            return

        # Generate user ID based on username and system info
        username = getpass.getuser()
        hostname = socket.gethostname()
        ca_fingerprint = self.get_ca_fingerprint()[:16]  # Use part of CA fingerprint

        # Create a stable but unique user ID
        user_data = f"{username}@{hostname}:{ca_fingerprint}"
        user_id_hash = hashlib.sha256(user_data.encode()).hexdigest()[:12]  # 12 chars

        with open(self.user_id_path, 'w') as f:
            f.write(user_id_hash)

    def get_user_id(self):
        """Get the user ID for this personal CA."""
        with open(self.user_id_path, 'r') as f:
            return f.read().strip()
