# Distributed Bus Framework

A framework for developing Multi-Personal-Node (MPN) applications that work seamlessly across multiple personal devices like smartphones, tablets, and PCs.

## Features

- **Service Discovery**: Automatic discovery of services across personal nodes using encrypted mDNS
- **RPC Framework**: Annotation-based remote procedure calls with secure communication
- **Event Delivery**: Publish-subscribe event system for asynchronous communication
- **Personal CA**: Certificate authority for secure node authentication
- **Cross-Platform**: Supports Android, macOS, Windows, and Linux

## Quick Start

### Installation

```bash
conda create -n distributed-bus python=3.11 -y
conda activate distributed-bus
pip install -r requirements.txt
```

### Basic Usage

#### Creating a Service

```python
from DistributedBus import service

@service(name="FileService")
class FileService:
    def open(self, filename, mode):
        return open(filename, mode).read()
```

#### Using a Service

```python
from DistributedBus import DistributedBus

bus = DistributedBus().start()
file_service = bus.lookup("FileService")
content = file_service.open("test.txt", "r")
```

#### Event Publishing and Subscribing

```python
from DistributedBus import DistributedBus, event_subscriber

# Publishing events
bus = DistributedBus().start()
bus.publish_event("data_changed", {"key": "value"})

# Subscribing to events
@event_subscriber("data_changed")
def handle_data_change(event):
    print(f"Data changed: {event.data}")
```

## Architecture

The framework consists of:

1. **Distributed Bus**: Core component providing service discovery and communication
2. **Personal CA**: Certificate authority for secure authentication
3. **Service Registry**: Local registry of available services
4. **RPC System**: XML-RPC based remote procedure calls
5. **Event System**: Publish-subscribe event delivery

## Examples

See the `examples/` directory for complete application examples including:
- Memo application (RPC-style MPN application)
- File sharing service
- Real-time chat application

## License

MIT License
