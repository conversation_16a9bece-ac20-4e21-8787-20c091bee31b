# Getting Started with Distributed Bus Framework

## Overview

The Distributed Bus Framework enables you to build Multi-Personal-Node (MPN) applications that work seamlessly across multiple personal devices like smartphones, tablets, and PCs.

## Installation

### Prerequisites

- Python 3.11 or higher
- conda (recommended) or pip

### Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd DistributedBus
   ```

2. **Create conda environment**
   ```bash
   conda create -n distributed-bus python=3.11 -y
   conda activate distributed-bus
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

### 1. Simple Test

Run the simple test to verify everything is working:

```bash
python examples/simple_test.py
```

You should see output showing service registration, RPC calls, and event delivery.

### 2. Memo Application Demo

The memo application demonstrates converting a single-node application to MPN:

```bash
python examples/memo_app/run_demo.py
```

### 3. Running the Full Memo Application

**Terminal 1 - Start the service:**
```bash
python examples/memo_app/memo_service.py
```

**Terminal 2 - Start the GUI client:**
```bash
python examples/memo_app/memo_gui.py
```

You can run multiple GUI clients on different machines and they will all connect to the same service.

## Core Concepts

### 1. Service Registration

Use the `@service` decorator to mark a class as a distributed service:

```python
from DistributedBus import service

@service(name="MyService")
class MyService:
    def hello(self, name):
        return f"Hello {name}!"
```

### 2. Service Discovery and Usage

Use the DistributedBus to discover and use services:

```python
from DistributedBus import DistributedBus

bus = DistributedBus().start()
my_service = bus.lookup("MyService")
result = my_service.hello("World")
```

### 3. Event Publishing and Subscribing

**Publishing events:**
```python
bus.publish_event("data_changed", {"key": "value"})
```

**Subscribing to events:**
```python
from DistributedBus import event_subscriber

@event_subscriber("data_changed")
def handle_data_change(event):
    print(f"Data changed: {event.data}")
```

## Converting Single-Node to MPN Applications

The framework makes it easy to convert existing applications. You only need 3 changes:

### Before (Single-Node):
```python
# Create local instance
memo_manager = MemoManager()

# Use the service
memo_manager.add_memo("Title", "Content")
```

### After (Multi-Personal-Node):
```python
# CHANGE 1: Initialize DistributedBus
bus = DistributedBus().start()

# CHANGE 2: Get local or remote service
memo_manager = bus.lookup("MemoService")

# CHANGE 3: Add annotation to service class
@service(name="MemoService")
class MemoManager:
    # ... existing code unchanged

# Original code works unchanged
memo_manager.add_memo("Title", "Content")
```

## Architecture

The framework consists of:

1. **Personal CA**: Certificate authority for secure authentication
2. **Service Discovery**: Encrypted mDNS for automatic service discovery
3. **RPC Framework**: XML-RPC with SSL for secure communication
4. **Event System**: Publish-subscribe for asynchronous communication
5. **Distributed Bus**: Main API that integrates all components

## Security

- All communication is encrypted using SSL/TLS
- Personal Certificate Authority ensures only your devices can communicate
- Service discovery uses encrypted mDNS announcements
- Each node has unique certificates signed by your personal CA

## Multi-Platform Support

The framework is designed to work on:
- **macOS**: Full support
- **Linux**: Full support  
- **Windows**: Full support
- **Android**: Requires Python runtime (e.g., Termux, Kivy)
- **iOS**: Requires Python runtime (e.g., Pythonista)

## Next Steps

1. Try the examples in the `examples/` directory
2. Read the API documentation in `docs/API.md`
3. Check out advanced examples for different application styles
4. Build your own MPN application!

## Troubleshooting

### Common Issues

1. **Service not found**: Make sure the service is running and both nodes are on the same network
2. **SSL errors**: Check that certificates are properly generated in `~/.distributed_bus_ca`
3. **Port conflicts**: The framework automatically finds available ports
4. **Network issues**: Ensure multicast is enabled on your network

### Debug Mode

Set environment variable for verbose logging:
```bash
export DISTRIBUTED_BUS_DEBUG=1
python your_app.py
```
