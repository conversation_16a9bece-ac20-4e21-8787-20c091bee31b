# API Reference

## DistributedBus Class

The main class for the Distributed Bus framework.

### Constructor

```python
DistributedBus(ca_dir="~/.distributed_bus_ca")
```

**Parameters:**
- `ca_dir` (str): Directory for personal CA certificates

### Methods

#### start()
Start the distributed bus services.

**Returns:** `DistributedBus` - Self for method chaining

```python
bus = DistributedBus().start()
```

#### lookup(service_name, timeout=5.0)
Look up a service by name.

**Parameters:**
- `service_name` (str): Name of the service to look up
- `timeout` (float): Maximum time to wait for service discovery

**Returns:** Service proxy object or `None` if not found

```python
service = bus.lookup("MyService", timeout=10.0)
```

#### register_service(service_name, service_instance)
Manually register a service instance.

**Parameters:**
- `service_name` (str): Name of the service
- `service_instance` (Any): Instance of the service class

```python
bus.register_service("MyService", MyService())
```

#### publish_event(event_name, data)
Publish an event to all subscribers.

**Parameters:**
- `event_name` (str): Name of the event
- `data` (Any): Event data

```python
bus.publish_event("data_changed", {"key": "value"})
```

#### subscribe(event_name, handler)
Subscribe to an event.

**Parameters:**
- `event_name` (str): Name of the event to subscribe to
- `handler` (Callable): Function to call when event is received

```python
def my_handler(event):
    print(event.data)

bus.subscribe("data_changed", my_handler)
```

#### wait_for_service(service_name, timeout=30.0)
Wait for a service to become available.

**Parameters:**
- `service_name` (str): Name of the service to wait for
- `timeout` (float): Maximum time to wait

**Returns:** Service proxy object or `None` if timeout

```python
service = bus.wait_for_service("MyService", timeout=60.0)
```

#### list_services()
List all discovered services.

**Returns:** `Dict[str, EncryptedServiceInfo]` - Dictionary of service information

#### get_node_info()
Get information about this node.

**Returns:** `Dict` - Node information including CA fingerprint and services

#### close()
Close the distributed bus and clean up resources.

```python
bus.close()
```

## Decorators

### @service(name)
Decorator to mark a class as an RPC service.

**Parameters:**
- `name` (str): The name of the service for discovery

```python
@service(name="FileService")
class FileService:
    def open(self, filename, mode):
        return open(filename, mode).read()
```

### @event_subscriber(event_name)
Decorator to mark a function as an event subscriber.

**Parameters:**
- `event_name` (str): The name of the event to subscribe to

```python
@event_subscriber("data_changed")
def handle_data_change(event):
    print(f"Data changed: {event.data}")
```

## Event Data

### EventData Class

Container for event data passed to event handlers.

**Attributes:**
- `event_name` (str): Name of the event
- `data` (Any): Event data payload
- `timestamp` (float): Unix timestamp when event was created
- `source_node` (str): ID of the node that published the event

```python
@event_subscriber("my_event")
def handle_event(event):
    print(f"Event: {event.event_name}")
    print(f"Data: {event.data}")
    print(f"From: {event.source_node}")
    print(f"Time: {event.timestamp}")
```

## Context Manager Support

The DistributedBus class supports context manager protocol:

```python
with DistributedBus().start() as bus:
    service = bus.lookup("MyService")
    # Use service...
# Bus is automatically closed
```

## Error Handling

### Common Exceptions

- `RuntimeError`: Raised when trying to use bus before calling `start()`
- `ConnectionError`: Raised when unable to connect to remote service
- `TimeoutError`: Raised when service lookup times out

### Best Practices

```python
try:
    bus = DistributedBus().start()
    service = bus.lookup("MyService")
    
    if service is None:
        print("Service not available")
        return
    
    result = service.some_method()
    
except ConnectionError as e:
    print(f"Connection failed: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
finally:
    bus.close()
```

## Advanced Usage

### Custom Service Discovery Timeout

```python
# Wait longer for services in slow networks
service = bus.lookup("SlowService", timeout=30.0)
```

### Manual Service Registration

```python
# Register service without decorator
class MyService:
    def hello(self):
        return "Hello!"

bus.register_service("ManualService", MyService())
```

### Event Filtering

```python
@event_subscriber("filtered_event")
def handle_filtered_event(event):
    # Only process events from specific source
    if event.source_node == "trusted_node":
        process_event(event.data)
```

### Service Health Checking

```python
def check_service_health(bus, service_name):
    try:
        service = bus.lookup(service_name, timeout=1.0)
        if service:
            # Try a simple method call
            service.ping()  # Assuming service has ping method
            return True
    except:
        pass
    return False
```
