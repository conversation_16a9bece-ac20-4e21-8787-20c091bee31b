#!/usr/bin/env python3
"""
Test to verify that RPC doesn't connect to its own services.
"""

import time
import threading
from DistributedBus import DistributedBus, service


@service("TestService")
class TestService:
    """A simple test service."""
    
    def __init__(self):
        self.call_count = 0
    
    def ping(self):
        """Simple ping method."""
        self.call_count += 1
        return f"pong {self.call_count}"
    
    def get_call_count(self):
        """Get the number of times ping was called."""
        return self.call_count


def test_no_self_connection():
    """Test that a service doesn't connect to itself."""
    print("Testing that services don't connect to themselves...")
    
    # Create a bus instance
    bus = DistributedBus()
    bus.start()
    
    try:
        # Wait a moment for service registration to complete
        time.sleep(2)
        
        # Check what services are registered locally
        node_info = bus.get_node_info()
        print(f"Local services: {node_info['local_services']}")
        print(f"Discovered services: {node_info['discovered_services']}")
        
        # Try to lookup the TestService using the RPC client directly
        print("\nLooking up TestService with RPC client...")
        rpc_proxy = bus.rpc_client.lookup_service("TestService", timeout=3.0)

        if rpc_proxy is None:
            print("✓ PASS: RPC client found no remote TestService (correctly filtered out self)")

            # Verify that we can still use the service through the smart proxy
            smart_proxy = bus.lookup("TestService", timeout=3.0)
            result = smart_proxy.ping()
            print(f"Smart proxy service call result: {result}")

        else:
            print("✗ FAIL: RPC client found a remote TestService (likely connected to self)")

            # Let's check if this is actually our own service
            try:
                service_info = rpc_proxy._service_info
                print(f"Connected to service at {service_info.address}:{service_info.port}")

                # Check if this matches our local service
                import socket
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()

                if service_info.address == local_ip:
                    # Check if the port matches any of our local services
                    for service_name, server in bus.rpc_server.servers.items():
                        if service_name == "TestService":
                            server_port = server.server_address[1]
                            if service_info.port == server_port:
                                print("✗ FAIL: Connected to own service!")
                                return False

            except Exception as e:
                print(f"Error checking service details: {e}")

            print("✓ PASS: Connected to a different service (not self)")
        
        return True
        
    finally:
        bus.close()


def test_with_multiple_nodes():
    """Test with multiple nodes to ensure proper behavior."""
    print("\n" + "="*50)
    print("Testing with multiple nodes...")
    
    # Create two bus instances
    bus1 = DistributedBus()
    bus2 = DistributedBus()
    
    try:
        # Start both buses
        bus1.start()
        time.sleep(1)  # Let first bus register its services
        
        bus2.start()
        time.sleep(2)  # Let second bus discover first bus's services
        
        # Check what each bus sees
        node1_info = bus1.get_node_info()
        node2_info = bus2.get_node_info()
        
        print(f"Bus1 - Local: {node1_info['local_services']}, Discovered: {node1_info['discovered_services']}")
        print(f"Bus2 - Local: {node2_info['local_services']}, Discovered: {node2_info['discovered_services']}")
        
        # Bus2 should find Bus1's TestService and not create its own
        service_proxy = bus2.lookup("TestService", timeout=5.0)
        
        if service_proxy:
            print("✓ PASS: Bus2 found remote TestService")
            
            # Verify it's actually connecting to Bus1's service
            result = service_proxy.ping()
            print(f"Service call result: {result}")
            
            # Check Bus2's local services - should not have TestService
            if "TestService" not in node2_info['local_services']:
                print("✓ PASS: Bus2 did not create local TestService (found remote)")
            else:
                print("✗ FAIL: Bus2 created local TestService despite finding remote")
                
        else:
            print("✗ FAIL: Bus2 did not find Bus1's TestService")
            
        return True
        
    finally:
        bus1.close()
        bus2.close()


if __name__ == "__main__":
    print("Testing RPC self-connection fix...")
    
    # Test 1: Single node should not connect to itself
    success1 = test_no_self_connection()
    
    # Test 2: Multiple nodes should connect to each other properly
    success2 = test_with_multiple_nodes()
    
    if success1 and success2:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!")
