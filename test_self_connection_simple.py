#!/usr/bin/env python3
"""
Simple test to verify that RPC doesn't connect to its own services.
"""

import time
from DistributedBus import DistributedBus, service


@service("SimpleService")
class SimpleService:
    """A simple test service."""
    
    def hello(self):
        return "Hello from SimpleService"


def main():
    print("Testing RPC self-connection prevention...")
    
    # Create and start a bus
    bus = DistributedBus()
    bus.start()
    
    try:
        # Wait for service registration
        time.sleep(2)
        
        print(f"Local services: {bus.get_node_info()['local_services']}")
        print(f"Discovered services: {bus.get_node_info()['discovered_services']}")
        
        # Test direct RPC client lookup
        print("\nTesting RPC client lookup...")
        rpc_proxy = bus.rpc_client.lookup_service("SimpleService", timeout=2.0)
        
        if rpc_proxy is None:
            print("✓ SUCCESS: RPC client correctly filtered out self-service")
        else:
            print("✗ FAILURE: RPC client found self-service")
            return False
        
        # Test smart proxy (should work via local fallback)
        print("\nTesting smart proxy...")
        smart_proxy = bus.lookup("SimpleService")
        result = smart_proxy.hello()
        print(f"Smart proxy result: {result}")
        
        if "Hello from SimpleService" in result:
            print("✓ SUCCESS: Smart proxy works with local fallback")
        else:
            print("✗ FAILURE: Smart proxy didn't work")
            return False
            
        return True
        
    finally:
        bus.close()


if __name__ == "__main__":
    if main():
        print("\n✓ All tests passed! Self-connection issue is fixed.")
    else:
        print("\n✗ Tests failed!")
