#!/usr/bin/env python3
"""
Linux Build Test Script for Distributed Memo App
Tests dependencies and potential build issues
"""

import sys
import subprocess
import importlib
import platform

def check_python_version():
    """Check Python version compatibility"""
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    print("✅ Python version OK")
    return True

def check_dependency(module_name, package_name=None):
    """Check if a dependency is available"""
    try:
        importlib.import_module(module_name)
        print(f"✅ {module_name} available")
        return True
    except ImportError:
        print(f"❌ {module_name} not available")
        if package_name:
            print(f"   Install with: pip install {package_name}")
        return False

def check_system_dependencies():
    """Check system-level dependencies for Linux"""
    print(f"\nSystem: {platform.system()} {platform.release()}")
    
    if platform.system() != 'Linux':
        print("⚠️  This test is designed for Linux systems")
        return True
    
    # Check for common Linux dependencies needed for Kivy
    dependencies = [
        'python3-dev',
        'python3-pip',
        'build-essential',
        'git',
        'ffmpeg',
        'libsdl2-dev',
        'libsdl2-image-dev',
        'libsdl2-mixer-dev',
        'libsdl2-ttf-dev',
        'libportmidi-dev',
        'libswscale-dev',
        'libavformat-dev',
        'libavcodec-dev',
        'zlib1g-dev'
    ]
    
    missing = []
    for dep in dependencies:
        try:
            result = subprocess.run(['dpkg', '-l', dep], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                missing.append(dep)
        except FileNotFoundError:
            print("⚠️  dpkg not found - cannot check system dependencies")
            return True
    
    if missing:
        print(f"❌ Missing system dependencies: {', '.join(missing)}")
        print("Install with:")
        print(f"sudo apt-get update && sudo apt-get install {' '.join(missing)}")
        return False
    else:
        print("✅ System dependencies OK")
        return True

def check_python_dependencies():
    """Check Python dependencies"""
    print("\nChecking Python dependencies...")
    
    dependencies = [
        ('kivy', 'kivy[base]'),
        ('zeroconf', 'zeroconf'),
        ('cryptography', 'cryptography'),
        ('requests', 'requests'),
    ]
    
    all_ok = True
    for module, package in dependencies:
        if not check_dependency(module, package):
            all_ok = False
    
    return all_ok

def test_kivy_import():
    """Test Kivy import and basic functionality"""
    print("\nTesting Kivy import...")
    try:
        import kivy
        print(f"✅ Kivy version: {kivy.__version__}")
        
        # Test basic Kivy components
        from kivy.app import App
        from kivy.uix.label import Label
        print("✅ Basic Kivy components OK")
        
        # Test graphics components
        from kivy.graphics import Color, Rectangle
        from kivy.utils import get_color_from_hex
        print("✅ Kivy graphics components OK")
        
        return True
    except Exception as e:
        print(f"❌ Kivy import failed: {e}")
        return False

def test_distributed_bus():
    """Test DistributedBus import"""
    print("\nTesting DistributedBus...")
    try:
        sys.path.insert(0, '../..')
        from DistributedBus import DistributedBus
        print("✅ DistributedBus import OK")
        return True
    except Exception as e:
        print(f"❌ DistributedBus import failed: {e}")
        return False

def test_buildozer_config():
    """Test buildozer configuration"""
    print("\nTesting buildozer configuration...")
    try:
        with open('buildozer.spec', 'r') as f:
            content = f.read()
        
        # Check for potential issues
        issues = []
        
        if 'requirements =' in content:
            req_line = [line for line in content.split('\n') if line.startswith('requirements =')][0]
            requirements = req_line.split('=')[1].strip().split(',')
            requirements = [r.strip() for r in requirements]
            
            # Check for problematic requirements
            problematic = ['pyjnius', 'android']
            for req in requirements:
                if req in problematic and platform.system() == 'Linux':
                    issues.append(f"Requirement '{req}' may cause issues on Linux")
        
        if issues:
            print("⚠️  Potential buildozer issues:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ Buildozer config looks OK")
        
        return len(issues) == 0
    except FileNotFoundError:
        print("❌ buildozer.spec not found")
        return False
    except Exception as e:
        print(f"❌ Error reading buildozer.spec: {e}")
        return False

def main():
    """Run all tests"""
    print("🔍 Linux Build Compatibility Test")
    print("=" * 40)
    
    tests = [
        ("Python Version", check_python_version),
        ("System Dependencies", check_system_dependencies),
        ("Python Dependencies", check_python_dependencies),
        ("Kivy Import", test_kivy_import),
        ("DistributedBus Import", test_distributed_bus),
        ("Buildozer Config", test_buildozer_config),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*20} SUMMARY {'='*20}")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Linux build should work.")
    else:
        print("⚠️  Some tests failed. Check the issues above.")
        print("\nCommon solutions:")
        print("1. Install missing system dependencies")
        print("2. Update pip: python3 -m pip install --upgrade pip")
        print("3. Install Kivy dependencies: pip install kivy[base]")
        print("4. For Android build, use buildozer in a proper environment")

if __name__ == '__main__':
    main()
