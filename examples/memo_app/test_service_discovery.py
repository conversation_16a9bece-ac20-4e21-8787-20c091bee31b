#!/usr/bin/env python3
"""
Test script to verify service discovery is working
"""

import sys
import os
import time

# Add DistributedBus to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from DistributedBus import DistributedBus

def test_service_discovery():
    """Test service discovery functionality"""
    print("🔍 Testing Service Discovery")
    print("=" * 40)
    
    # Initialize DistributedBus
    bus = DistributedBus().start()
    
    # Wait a moment for discovery
    print("Waiting for service discovery...")
    time.sleep(3)
    
    # List all discovered services
    services = bus.service_discovery.list_services()
    print(f"\n📡 Discovered {len(services)} services:")
    
    for service_name, service_info in services.items():
        print(f"  - {service_name}: {service_info.address}:{service_info.port}")
    
    # Try to lookup MemoService
    print(f"\n🔍 Looking up MemoService...")
    memo_service = bus.lookup("MemoService")
    
    if memo_service:
        print(f"✅ Found MemoService: {type(memo_service)}")
        
        # Test if it's a remote service
        if hasattr(memo_service, '_service_info'):
            print(f"   Remote service at: {memo_service._service_info.address}:{memo_service._service_info.port}")
        else:
            print("   Local service instance")
            
        # Test a simple method call
        try:
            memos = memo_service.get_all_memos()
            print(f"   Service call successful: {len(memos)} memos")
        except Exception as e:
            print(f"   Service call failed: {e}")
    else:
        print("❌ MemoService not found")
    
    # Test service discovery methods
    print(f"\n🔍 Testing discovery methods...")
    
    # Test lookup_service directly
    direct_lookup = bus.service_discovery.lookup_service("MemoService")
    if direct_lookup:
        print(f"✅ Direct lookup found: {direct_lookup.service_name}")
    else:
        print("❌ Direct lookup failed")
    
    # Test pattern matching
    memo_services = []
    for name, info in services.items():
        if name.startswith("MemoService"):
            memo_services.append((name, info))
    
    print(f"✅ Found {len(memo_services)} MemoService instances:")
    for name, info in memo_services:
        print(f"   - {name}: {info.address}:{info.port}")
    
    print(f"\n🎯 Summary:")
    print(f"   Total services discovered: {len(services)}")
    print(f"   MemoService instances: {len(memo_services)}")
    print(f"   Service lookup successful: {'Yes' if memo_service else 'No'}")
    
    # Cleanup
    bus.close()

if __name__ == '__main__':
    test_service_discovery()
