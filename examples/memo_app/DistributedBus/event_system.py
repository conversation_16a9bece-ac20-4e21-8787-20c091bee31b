"""
Event delivery system using publish-subscribe model.
"""

import json
import socket
import threading
import time
from typing import Dict, List, Callable, Any
import uuid

from .decorators import get_event_subscribers, EventData
from .personal_ca import PersonalCA


class EventPublisher:
    """Publisher for sending events to subscribers."""
    
    def __init__(self, personal_ca: PersonalCA, multicast_group: str = "224.1.1.1", port: int = 5007):
        self.personal_ca = personal_ca
        self.multicast_group = multicast_group
        self.port = port
        self.node_id = str(uuid.uuid4())
        
        # Create multicast socket for publishing
        self.pub_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.pub_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        # Set TTL for multicast
        ttl = 2  # Local network only
        self.pub_socket.setsockopt(socket.IPPROTO_IP, socket.IP_MULTICAST_TTL, ttl)
    
    def publish_event(self, event_name: str, data: Any):
        """Publish an event to all subscribers."""
        try:
            # Create event message
            event_message = {
                "event_name": event_name,
                "data": data,
                "timestamp": time.time(),
                "source_node": self.node_id,
                "ca_fingerprint": self.personal_ca.get_ca_fingerprint()
            }
            
            # Serialize and send
            message_json = json.dumps(event_message)
            self.pub_socket.sendto(
                message_json.encode('utf-8'),
                (self.multicast_group, self.port)
            )
            
            print(f"Published event: {event_name}")
            
        except Exception as e:
            print(f"Failed to publish event {event_name}: {e}")
    
    def close(self):
        """Close the publisher."""
        self.pub_socket.close()


class EventSubscriber:
    """Subscriber for receiving events."""
    
    def __init__(self, personal_ca: PersonalCA, multicast_group: str = "224.1.1.1", port: int = 5007):
        self.personal_ca = personal_ca
        self.multicast_group = multicast_group
        self.port = port
        self.node_id = str(uuid.uuid4())

        # Event handlers
        self.event_handlers: Dict[str, List[Callable]] = {}

        # Create multicast socket for subscribing
        self.sub_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.sub_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.sub_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)

        # Bind to multicast group
        self.sub_socket.bind(('', self.port))
        
        # Join multicast group
        mreq = socket.inet_aton(self.multicast_group) + socket.inet_aton('0.0.0.0')
        self.sub_socket.setsockopt(socket.IPPROTO_IP, socket.IP_ADD_MEMBERSHIP, mreq)
        
        # Start listening thread
        self.listening = True
        self.listen_thread = threading.Thread(target=self._listen_for_events, daemon=True)
        self.listen_thread.start()
        
        # Register decorated event subscribers
        self._register_decorated_subscribers()
    
    def _register_decorated_subscribers(self):
        """Register functions decorated with @event_subscriber."""
        subscribers = get_event_subscribers()
        for event_name, handlers in subscribers.items():
            for handler in handlers:
                self.subscribe(event_name, handler)
    
    def subscribe(self, event_name: str, handler: Callable):
        """Subscribe to an event."""
        if event_name not in self.event_handlers:
            self.event_handlers[event_name] = []
        
        self.event_handlers[event_name].append(handler)
        print(f"Subscribed to event: {event_name}")
    
    def unsubscribe(self, event_name: str, handler: Callable):
        """Unsubscribe from an event."""
        if event_name in self.event_handlers:
            try:
                self.event_handlers[event_name].remove(handler)
                if not self.event_handlers[event_name]:
                    del self.event_handlers[event_name]
                print(f"Unsubscribed from event: {event_name}")
            except ValueError:
                pass
    
    def _listen_for_events(self):
        """Listen for incoming events."""
        while self.listening:
            try:
                data, addr = self.sub_socket.recvfrom(4096)
                self._handle_event_message(data.decode('utf-8'))
            except Exception as e:
                if self.listening:  # Only log if we're still supposed to be listening
                    print(f"Error receiving event: {e}")
    
    def _handle_event_message(self, message_json: str):
        """Handle an incoming event message."""
        try:
            event_message = json.loads(message_json)
            
            # Verify CA fingerprint
            if event_message.get("ca_fingerprint") != self.personal_ca.get_ca_fingerprint():
                return  # Ignore events from other personal CAs
            
            # Don't process our own events
            if event_message.get("source_node") == self.node_id:
                return
            
            event_name = event_message["event_name"]
            
            # Create event data object
            event_data = EventData(
                event_name=event_name,
                data=event_message["data"],
                timestamp=event_message["timestamp"],
                source_node=event_message.get("source_node")
            )
            
            # Call handlers
            if event_name in self.event_handlers:
                for handler in self.event_handlers[event_name]:
                    try:
                        handler(event_data)
                    except Exception as e:
                        print(f"Error in event handler for {event_name}: {e}")
            
        except Exception as e:
            print(f"Error handling event message: {e}")
    
    def close(self):
        """Close the subscriber."""
        self.listening = False
        self.sub_socket.close()
        if self.listen_thread.is_alive():
            self.listen_thread.join(timeout=1.0)


class EventSystem:
    """Combined event system with publisher and subscriber."""
    
    def __init__(self, personal_ca: PersonalCA):
        self.personal_ca = personal_ca
        self.publisher = EventPublisher(personal_ca)
        self.subscriber = EventSubscriber(personal_ca)
    
    def publish_event(self, event_name: str, data: Any):
        """Publish an event."""
        self.publisher.publish_event(event_name, data)
    
    def subscribe(self, event_name: str, handler: Callable):
        """Subscribe to an event."""
        self.subscriber.subscribe(event_name, handler)
    
    def unsubscribe(self, event_name: str, handler: Callable):
        """Unsubscribe from an event."""
        self.subscriber.unsubscribe(event_name, handler)
    
    def close(self):
        """Close the event system."""
        self.publisher.close()
        self.subscriber.close()
