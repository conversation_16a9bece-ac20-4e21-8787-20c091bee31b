"""
Cross-platform Memo Application GUI using Kivy
Works on both Android and macOS
"""

import os
import sys
from datetime import datetime
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.uix.textinput import TextInput
from kivy.uix.scrollview import ScrollView
from kivy.uix.gridlayout import GridLayout
from kivy.uix.popup import Popup
from kivy.uix.screenmanager import ScreenManager, Screen
from kivy.clock import Clock
from kivy.graphics import Color, Rectangle
from kivy.utils import get_color_from_hex

# CHANGE 1: Add DistributedBus to path and import distributed components
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))
from DistributedBus import DistributedBus, service
from memo_manager import MemoManager  # This already has @service annotation

# Native-like color scheme
COLORS = {
    'background': get_color_from_hex('#F5F5F7'),  # Light gray background
    'surface': get_color_from_hex('#FFFFFF'),     # White surface
    'primary': get_color_from_hex('#007AFF'),     # iOS blue
    'secondary': get_color_from_hex('#5856D6'),   # iOS purple
    'text_primary': get_color_from_hex('#000000'), # Black text
    'text_secondary': get_color_from_hex('#8E8E93'), # Gray text
    'border': get_color_from_hex('#C6C6C8'),      # Light border
    'success': get_color_from_hex('#34C759'),     # Green
    'danger': get_color_from_hex('#FF3B30'),      # Red
}

class StyledButton(Button):
    def __init__(self, style='primary', **kwargs):
        super().__init__(**kwargs)
        self.style = style
        self.background_normal = ''
        self.background_down = ''
        
        if self.style in ['primary', 'secondary', 'danger', 'success']:
            self.color = [0, 0, 0, 1]  # 黑色文字
        else:
            self.color = [1, 1, 1, 1]  # 白色文字
        self.bind(size=self.update_graphics, pos=self.update_graphics)
        self.update_graphics()

    def update_graphics(self, *args):
        self.canvas.before.clear()
        with self.canvas.before:
            if self.style == 'primary':
                Color(*COLORS['primary'])
            elif self.style == 'secondary':
                Color(*COLORS['secondary'])
            elif self.style == 'danger':
                Color(*COLORS['danger'])
            elif self.style == 'success':
                Color(*COLORS['success'])
            else:
                Color(*COLORS['surface'])
            Rectangle(pos=self.pos, size=self.size)

class StyledTextInput(TextInput):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.background_normal = ''
        self.background_active = ''
        self.cursor_color = COLORS['primary']
        self.foreground_color = COLORS['text_primary']
        self.bind(size=self.update_graphics, pos=self.update_graphics)
        self.update_graphics()

    def update_graphics(self, *args):
        self.canvas.before.clear()
        with self.canvas.before:
            Color(*COLORS['surface'])
            Rectangle(pos=self.pos, size=self.size)
            Color(*COLORS['border'])
            # Border
            Rectangle(pos=self.pos, size=(self.width, 1))  # Bottom
            Rectangle(pos=self.pos, size=(1, self.height))  # Left
            Rectangle(pos=(self.x + self.width - 1, self.y), size=(1, self.height))  # Right
            Rectangle(pos=(self.x, self.y + self.height - 1), size=(self.width, 1))  # Top

class StyledLabel(Label):
    def __init__(self, style='primary', **kwargs):
        super().__init__(**kwargs)
        self.style = style
        if style == 'primary':
            self.color = COLORS['text_primary']
        elif style == 'secondary':
            self.color = COLORS['text_secondary']
        elif style == 'title':
            self.color = COLORS['text_primary']
            self.bold = True


class MemoListScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # CHANGE 2: Initialize distributed system instead of direct MemoManager
        self.memo_manager = None
        self.bus = None
        self.build_ui()
        self.setup_distributed_system()

    def setup_distributed_system(self):
        """CHANGE 3: Setup distributed system instead of direct instantiation"""
        self.bus = DistributedBus().start()
        self.memo_manager = self.bus.lookup("MemoService")
        print("DistributedBus initialized successfully")
        print(f"MemoService obtained: {self.memo_manager is not None}")
        Clock.schedule_once(lambda dt: self.refresh_memo_list(), 1)

    def build_ui(self):
        # Set background color
        with self.canvas.before:
            Color(*COLORS['background'])
            self.bg_rect = Rectangle(size=self.size, pos=self.pos)
        self.bind(size=self._update_bg, pos=self._update_bg)

        main_layout = BoxLayout(orientation='vertical', padding=20, spacing=15)

        # Title
        title = StyledLabel(text='Memo App', size_hint_y=None, height=60, font_size=28, style='title')
        main_layout.add_widget(title)

        # Search bar
        search_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=50, spacing=10)
        self.search_input = StyledTextInput(hint_text='Search memos...', multiline=False)
        search_btn = StyledButton(text='Search', size_hint_x=None, width=100, style='primary')
        search_btn.bind(on_press=self.search_memos)
        search_layout.add_widget(self.search_input)
        search_layout.add_widget(search_btn)
        main_layout.add_widget(search_layout)

        # Memo list
        self.memo_scroll = ScrollView()
        self.memo_list = GridLayout(cols=1, size_hint_y=None, spacing=10)
        self.memo_list.bind(minimum_height=self.memo_list.setter('height'))
        self.memo_scroll.add_widget(self.memo_list)
        main_layout.add_widget(self.memo_scroll)

        # Buttons
        button_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=60, spacing=15)

        add_btn = StyledButton(text='Add Memo', style='primary')
        add_btn.bind(on_press=self.show_add_memo)

        refresh_btn = StyledButton(text='Refresh', style='secondary')
        refresh_btn.bind(on_press=lambda x: self.refresh_memo_list())

        clear_btn = StyledButton(text='Clear All', style='danger')
        clear_btn.bind(on_press=self.clear_all_memos)

        button_layout.add_widget(add_btn)
        button_layout.add_widget(refresh_btn)
        button_layout.add_widget(clear_btn)

        main_layout.add_widget(button_layout)
        self.add_widget(main_layout)

    def _update_bg(self, *args):
        self.bg_rect.size = self.size
        self.bg_rect.pos = self.pos

    def refresh_memo_list(self):
        if not self.memo_manager:
            return

        self.memo_list.clear_widgets()
        memos = self.memo_manager.get_all_memos()

        if not memos:
            no_memos = StyledLabel(text='No memos yet. Tap "Add Memo" to create one!',
                                 size_hint_y=None, height=60, style='secondary')
            self.memo_list.add_widget(no_memos)
            return

        # Sort by updated_at descending
        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)

        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)

    def create_memo_widget(self, memo):
        # Create a card-like container
        memo_layout = BoxLayout(orientation='vertical', size_hint_y=None, height=140,
                               padding=15, spacing=8)

        # Add background
        with memo_layout.canvas.before:
            Color(*COLORS['surface'])
            memo_layout.bg_rect = Rectangle(pos=memo_layout.pos, size=memo_layout.size)
            Color(*COLORS['border'])
            # Add subtle border
            memo_layout.border_rect = Rectangle(pos=memo_layout.pos, size=memo_layout.size)

        def update_memo_bg(*args):
            memo_layout.bg_rect.pos = memo_layout.pos
            memo_layout.bg_rect.size = memo_layout.size
            memo_layout.border_rect.pos = memo_layout.pos
            memo_layout.border_rect.size = memo_layout.size

        memo_layout.bind(pos=update_memo_bg, size=update_memo_bg)

        # Title and date
        header_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=35)
        title_label = StyledLabel(text=memo['title'], font_size=18, style='title',
                                text_size=(None, None), halign='left')

        # Format date
        date_str = datetime.fromtimestamp(memo['updated_at']).strftime('%m/%d %H:%M')
        date_label = StyledLabel(text=date_str, size_hint_x=None, width=100,
                               font_size=14, style='secondary')

        header_layout.add_widget(title_label)
        header_layout.add_widget(date_label)
        memo_layout.add_widget(header_layout)

        # Content preview
        content_preview = memo['content'][:100] + ('...' if len(memo['content']) > 100 else '')
        content_label = StyledLabel(text=content_preview, text_size=(None, None),
                                  font_size=14, halign='left', style='primary')
        memo_layout.add_widget(content_label)

        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=40, spacing=10)

        edit_btn = StyledButton(text='Edit', size_hint_x=None, width=80, style='primary')
        edit_btn.bind(on_press=lambda x, m=memo: self.show_edit_memo(m))

        delete_btn = StyledButton(text='Delete', size_hint_x=None, width=80, style='danger')
        delete_btn.bind(on_press=lambda x, m=memo: self.delete_memo(m))

        btn_layout.add_widget(edit_btn)
        btn_layout.add_widget(delete_btn)
        btn_layout.add_widget(Label())  # Spacer

        memo_layout.add_widget(btn_layout)

        return memo_layout
    
    def search_memos(self, instance):
        query = self.search_input.text.strip()
        if query:
            memos = self.memo_manager.search_memos(query)
        else:
            memos = self.memo_manager.get_all_memos()

        self.memo_list.clear_widgets()

        if not memos:
            no_results = StyledLabel(text='No memos found.', size_hint_y=None, height=60, style='secondary')
            self.memo_list.add_widget(no_results)
            return

        memos.sort(key=lambda x: x.get('updated_at', 0), reverse=True)
        for memo in memos:
            memo_widget = self.create_memo_widget(memo)
            self.memo_list.add_widget(memo_widget)

    def show_add_memo(self, instance):
        self.manager.get_screen('edit').setup_for_add()
        self.manager.current = 'edit'

    def show_edit_memo(self, memo):
        self.manager.get_screen('edit').setup_for_edit(memo)
        self.manager.current = 'edit'

    def delete_memo(self, memo):
        self.memo_manager.delete_memo(memo['id'])
        self.refresh_memo_list()

    def clear_all_memos(self, instance):
        popup_layout = BoxLayout(orientation='vertical', spacing=15, padding=20)

        # Set background
        with popup_layout.canvas.before:
            Color(*COLORS['surface'])
            popup_layout.bg_rect = Rectangle(pos=popup_layout.pos, size=popup_layout.size)
        popup_layout.bind(pos=lambda *args: setattr(popup_layout.bg_rect, 'pos', popup_layout.pos),
                         size=lambda *args: setattr(popup_layout.bg_rect, 'size', popup_layout.size))

        popup_layout.add_widget(StyledLabel(text='Are you sure you want to clear all memos?',
                                          style='primary', font_size=16))

        btn_layout = BoxLayout(orientation='horizontal', spacing=15, size_hint_y=None, height=50)
        yes_btn = StyledButton(text='Yes', style='danger')
        no_btn = StyledButton(text='No', style='secondary')

        popup = Popup(title='Confirm Clear All', content=popup_layout, size_hint=(0.8, 0.4),
                     background='', separator_color=COLORS['border'])

        yes_btn.bind(on_press=lambda x: self.confirm_clear_all(popup))
        no_btn.bind(on_press=popup.dismiss)

        btn_layout.add_widget(yes_btn)
        btn_layout.add_widget(no_btn)
        popup_layout.add_widget(btn_layout)

        popup.open()

    def confirm_clear_all(self, popup):
        self.memo_manager.clear_all_memos()
        self.refresh_memo_list()
        popup.dismiss()


class MemoEditScreen(Screen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.memo_manager = None
        self.current_memo = None
        self.build_ui()
    
    def build_ui(self):
        # Set background color
        with self.canvas.before:
            Color(*COLORS['background'])
            self.bg_rect = Rectangle(size=self.size, pos=self.pos)
        self.bind(size=self._update_bg, pos=self._update_bg)

        main_layout = BoxLayout(orientation='vertical', padding=20, spacing=15)

        # Title
        self.screen_title = StyledLabel(text='Add Memo', size_hint_y=None, height=60,
                                      font_size=24, style='title')
        main_layout.add_widget(self.screen_title)

        # Title input
        main_layout.add_widget(StyledLabel(text='Title:', size_hint_y=None, height=30,
                                         style='primary', halign='left'))
        self.title_input = StyledTextInput(multiline=False, size_hint_y=None, height=50)
        main_layout.add_widget(self.title_input)

        # Content input
        main_layout.add_widget(StyledLabel(text='Content:', size_hint_y=None, height=30,
                                         style='primary', halign='left'))
        self.content_input = StyledTextInput(multiline=True)
        main_layout.add_widget(self.content_input)

        # Tags input
        main_layout.add_widget(StyledLabel(text='Tags (comma separated):', size_hint_y=None,
                                         height=30, style='primary', halign='left'))
        self.tags_input = StyledTextInput(multiline=False, size_hint_y=None, height=50)
        main_layout.add_widget(self.tags_input)

        # Buttons
        btn_layout = BoxLayout(orientation='horizontal', size_hint_y=None, height=60, spacing=15)

        save_btn = StyledButton(text='Save', style='success')
        save_btn.bind(on_press=self.save_memo)

        cancel_btn = StyledButton(text='Cancel', style='secondary')
        cancel_btn.bind(on_press=self.cancel_edit)

        btn_layout.add_widget(save_btn)
        btn_layout.add_widget(cancel_btn)

        main_layout.add_widget(btn_layout)
        self.add_widget(main_layout)

    def _update_bg(self, *args):
        self.bg_rect.size = self.size
        self.bg_rect.pos = self.pos

    def setup_for_add(self):
        self.screen_title.text = 'Add Memo'
        self.current_memo = None
        self.title_input.text = ''
        self.content_input.text = ''
        self.tags_input.text = ''
        self.memo_manager = self.manager.get_screen('list').memo_manager

    def setup_for_edit(self, memo):
        self.screen_title.text = 'Edit Memo'
        self.current_memo = memo
        self.title_input.text = memo['title']
        self.content_input.text = memo['content']
        self.tags_input.text = ', '.join(memo['tags'])
        self.memo_manager = self.manager.get_screen('list').memo_manager

    def save_memo(self, instance):
        title = self.title_input.text.strip()
        content = self.content_input.text.strip()
        tags_text = self.tags_input.text.strip()

        if not title:
            self.show_error('Title is required!')
            return

        tags = [tag.strip() for tag in tags_text.split(',') if tag.strip()] if tags_text else []

        if self.current_memo:
            # Update existing memo
            self.memo_manager.update_memo(self.current_memo['id'], title, content, tags)
        else:
            # Add new memo
            self.memo_manager.add_memo(title, content, tags)

        # Refresh the list and go back
        self.manager.get_screen('list').refresh_memo_list()
        self.manager.current = 'list'

    def cancel_edit(self, instance):
        self.manager.current = 'list'

    def show_error(self, message):
        popup_layout = BoxLayout(orientation='vertical', spacing=15, padding=20)

        # Set background
        with popup_layout.canvas.before:
            Color(*COLORS['surface'])
            popup_layout.bg_rect = Rectangle(pos=popup_layout.pos, size=popup_layout.size)
        popup_layout.bind(pos=lambda *args: setattr(popup_layout.bg_rect, 'pos', popup_layout.pos),
                         size=lambda *args: setattr(popup_layout.bg_rect, 'size', popup_layout.size))

        popup_layout.add_widget(StyledLabel(text=message, style='primary', font_size=16))

        ok_btn = StyledButton(text='OK', style='primary', size_hint_y=None, height=50)
        popup = Popup(title='Error', content=popup_layout, size_hint=(0.8, 0.4),
                     background='', separator_color=COLORS['border'])
        ok_btn.bind(on_press=popup.dismiss)
        popup_layout.add_widget(ok_btn)
        popup.open()


class MemoApp(App):
    def build(self):
        sm = ScreenManager()

        list_screen = MemoListScreen(name='list')
        edit_screen = MemoEditScreen(name='edit')

        sm.add_widget(list_screen)
        sm.add_widget(edit_screen)

        return sm


if __name__ == '__main__':
    MemoApp().run()
